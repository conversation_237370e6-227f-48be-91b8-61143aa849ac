package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/clients"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/handlers/dto"
	"gitlab.zalopay.vn/phunn4/coupon-api-gateway/internal/utils"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/auth"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"google.golang.org/protobuf/types/known/timestamppb"

	proto_common_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/common/v1"
	proto_voucher_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/voucher/v1"
)

type VoucherHandler struct {
	voucherClient *clients.VoucherClient
	logger        *logging.Logger
	validator     *validator.Validate
}

func NewVoucherHandler(voucherClient *clients.VoucherClient, logger *logging.Logger) *VoucherHandler {
	return &VoucherHandler{
		voucherClient: voucherClient,
		logger:        logger,
		validator:     validator.New(),
	}
}

func (h *VoucherHandler) RegisterPublicRoutes(g *echo.Group) {
	g.POST("/vouchers/check-eligibility", h.HandleCheckVoucherEligibility)
	g.POST("/vouchers/auto-eligible", h.HandleListAutoEligibleVouchers)
	g.GET("/vouchers/discount-types", h.HandleGetDiscountTypes)
}

func (h *VoucherHandler) RegisterProtectedRoutes(g *echo.Group) {
	g.POST("/vouchers", h.HandleCreateVoucher)
	g.GET("/vouchers/:id", h.HandleGetVoucher)
	g.GET("/vouchers/code/:code", h.HandleGetVoucherByCode)
	g.PUT("/vouchers/:id", h.HandleUpdateVoucher)
	g.GET("/vouchers", h.HandleListVouchers)
}

// CreateVoucher creates a new voucher
func (h *VoucherHandler) HandleCreateVoucher(c echo.Context) error {
	var req dto.CreateVoucherRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse("Invalid request body"))
	}

	if err := h.validator.Struct(&req); err != nil {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse("Validation failed: "+err.Error()))
	}

	userID, ok := auth.GetUserIDFromContext(c.Request().Context())
	if !ok || userID == "" {
		return c.JSON(http.StatusUnauthorized, utils.NewErrorResponse("invalid token: missing user_id"))
	}

	// Convert to proto request
	protoReq := &proto_voucher_v1.CreateVoucherRequest{
		Metadata: &proto_common_v1.RequestMetadata{
			RequestId: c.Response().Header().Get(echo.HeaderXRequestID),
			UserId:    userID,
		},
		VoucherCode:    req.VoucherCode,
		Title:          req.Title,
		Description:    req.Description,
		DiscountTypeId: req.DiscountTypeID,
		DiscountValue:  req.DiscountValue,
		UsageMethod:    dto.ConvertStringToUsageMethod(req.UsageMethod),
		ValidFrom:      timestamppb.New(req.ValidFrom),
		ValidUntil:     timestamppb.New(req.ValidUntil),
		MinOrderAmount: req.MinOrderAmount,
	}

	if req.MaxUsageCount != nil {
		protoReq.MaxUsageCount = req.MaxUsageCount
	}

	if req.MaxUsagePerUser != nil {
		protoReq.MaxUsagePerUser = req.MaxUsagePerUser
	}

	if req.MaxDiscountAmount != nil {
		protoReq.MaxDiscountAmount = req.MaxDiscountAmount
	}

	resp, err := h.voucherClient.CreateVoucher(c.Request().Context(), protoReq)
	if err != nil {
		return utils.HandleGRPCError(c, err, h.logger)
	}

	voucherResponse := dto.ToVoucherResponse(resp.Voucher)
	return c.JSON(http.StatusCreated, voucherResponse)
}

// GetVoucher retrieves a voucher by ID
func (h *VoucherHandler) HandleGetVoucher(c echo.Context) error {
	voucherID := c.Param("id")
	if voucherID == "" {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse("Voucher ID is required"))
	}

	userID, ok := auth.GetUserIDFromContext(c.Request().Context())
	if !ok || userID == "" {
		return c.JSON(http.StatusUnauthorized, utils.NewErrorResponse("invalid token: missing user_id"))
	}

	protoReq := &proto_voucher_v1.GetVoucherRequest{
		Metadata: &proto_common_v1.RequestMetadata{
			RequestId: c.Response().Header().Get(echo.HeaderXRequestID),
			UserId:    userID,
		},
		VoucherId: voucherID,
	}

	resp, err := h.voucherClient.GetVoucher(c.Request().Context(), protoReq)
	if err != nil {
		return utils.HandleGRPCError(c, err, h.logger)
	}

	voucherResponse := dto.ToVoucherResponse(resp.Voucher)
	return c.JSON(http.StatusOK, voucherResponse)
}

// GetVoucherByCode retrieves a voucher by code
func (h *VoucherHandler) HandleGetVoucherByCode(c echo.Context) error {
	voucherCode := c.Param("code")
	if voucherCode == "" {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse("Voucher code is required"))
	}

	userID, ok := auth.GetUserIDFromContext(c.Request().Context())
	if !ok || userID == "" {
		return c.JSON(http.StatusUnauthorized, utils.NewErrorResponse("invalid token: missing user_id"))
	}

	protoReq := &proto_voucher_v1.GetVoucherByCodeRequest{
		Metadata: &proto_common_v1.RequestMetadata{
			RequestId: c.Response().Header().Get(echo.HeaderXRequestID),
			UserId:    userID,
		},
		VoucherCode: voucherCode,
	}

	resp, err := h.voucherClient.GetVoucherByCode(c.Request().Context(), protoReq)
	if err != nil {
		return utils.HandleGRPCError(c, err, h.logger)
	}

	voucherResponse := dto.ToVoucherResponse(resp.Voucher)
	return c.JSON(http.StatusOK, voucherResponse)
}

// UpdateVoucher updates an existing voucher
func (h *VoucherHandler) HandleUpdateVoucher(c echo.Context) error {
	voucherID := c.Param("id")
	if voucherID == "" {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse("Voucher ID is required"))
	}

	var req dto.UpdateVoucherRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse("Invalid request body"))
	}

	if err := h.validator.Struct(&req); err != nil {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse("Validation failed: "+err.Error()))
	}

	userID, ok := auth.GetUserIDFromContext(c.Request().Context())
	if !ok || userID == "" {
		return c.JSON(http.StatusUnauthorized, utils.NewErrorResponse("invalid token: missing user_id"))
	}

	// Convert to proto request
	protoReq := &proto_voucher_v1.UpdateVoucherRequest{
		Metadata: &proto_common_v1.RequestMetadata{
			RequestId: c.Response().Header().Get(echo.HeaderXRequestID),
			UserId:    userID,
		},
		VoucherId:      voucherID,
		Title:          req.Title,
		Description:    req.Description,
		DiscountTypeId: req.DiscountTypeID,
		DiscountValue:  req.DiscountValue,
		UsageMethod:    dto.ConvertStringToUsageMethod(req.UsageMethod),
		Status:         dto.ConvertStringToVoucherStatus(req.Status),
		MinOrderAmount: req.MinOrderAmount,
		ValidFrom:      timestamppb.New(req.ValidFrom),
		ValidUntil:     timestamppb.New(req.ValidUntil),
	}

	if req.MaxUsageCount != nil {
		protoReq.MaxUsageCount = req.MaxUsageCount
	}

	if req.MaxUsagePerUser != nil {
		protoReq.MaxUsagePerUser = req.MaxUsagePerUser
	}

	if req.MaxDiscountAmount != nil {
		protoReq.MaxDiscountAmount = req.MaxDiscountAmount
	}

	resp, err := h.voucherClient.UpdateVoucher(c.Request().Context(), protoReq)
	if err != nil {
		return utils.HandleGRPCError(c, err, h.logger)
	}

	voucherResponse := dto.ToVoucherResponse(resp.Voucher)
	return c.JSON(http.StatusOK, voucherResponse)
}

// ListVouchers retrieves vouchers with pagination and filtering
func (h *VoucherHandler) HandleListVouchers(c echo.Context) error {
	userID, ok := auth.GetUserIDFromContext(c.Request().Context())
	if !ok || userID == "" {
		return c.JSON(http.StatusUnauthorized, utils.NewErrorResponse("invalid token: missing user_id"))
	}

	// Parse query parameters
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page <= 0 {
		page = 1
	}

	limit, _ := strconv.Atoi(c.QueryParam("limit"))
	if limit <= 0 {
		limit = 10
	}

	search := c.QueryParam("search")
	discountTypeID := c.QueryParam("discount_type_id")
	usageMethod := c.QueryParam("usage_method")
	status := c.QueryParam("status")
	sortBy := c.QueryParam("sort_by")
	sortOrder := c.QueryParam("sort_order")

	protoReq := &proto_voucher_v1.ListVouchersRequest{
		Metadata: &proto_common_v1.RequestMetadata{
			RequestId: c.Response().Header().Get(echo.HeaderXRequestID),
			UserId:    userID,
		},
		Page:      int32(page),
		Limit:     int32(limit),
		Search:    search,
		Status:    status,
		SortBy:    sortBy,
		SortOrder: sortOrder,
	}

	if discountTypeID != "" {
		protoReq.DiscountTypeId = &discountTypeID
	}

	if usageMethod != "" {
		method := dto.ConvertStringToUsageMethod(usageMethod)
		protoReq.UsageMethod = &method
	}

	resp, err := h.voucherClient.ListVouchers(c.Request().Context(), protoReq)
	if err != nil {
		return utils.HandleGRPCError(c, err, h.logger)
	}

	// Convert to response DTOs
	var vouchers []*dto.VoucherResponse
	for _, voucher := range resp.Vouchers {
		vouchers = append(vouchers, dto.ToVoucherResponse(voucher))
	}

	response := &dto.ListVouchersResponse{
		Data:       vouchers,
		Total:      resp.Total,
		Page:       resp.Page,
		Limit:      resp.Limit,
		TotalPages: resp.TotalPages,
	}

	return c.JSON(http.StatusOK, response)
}

// GetDiscountTypes retrieves all discount types
func (h *VoucherHandler) HandleGetDiscountTypes(c echo.Context) error {
	protoReq := &proto_voucher_v1.GetDiscountTypesRequest{
		Metadata: &proto_common_v1.RequestMetadata{
			RequestId: c.Response().Header().Get(echo.HeaderXRequestID),
		},
	}

	resp, err := h.voucherClient.GetDiscountTypes(c.Request().Context(), protoReq)
	if err != nil {
		return utils.HandleGRPCError(c, err, h.logger)
	}

	// Convert to response DTOs
	var discountTypes []*dto.DiscountTypeResponse
	for _, dt := range resp.DiscountTypes {
		discountTypes = append(discountTypes, dto.ToDiscountTypeResponse(dt))
	}

	return c.JSON(http.StatusOK, discountTypes)
}

// CheckVoucherEligibility checks if a voucher is eligible for use
func (h *VoucherHandler) HandleCheckVoucherEligibility(c echo.Context) error {
	var req dto.CheckVoucherEligibilityRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse("Invalid request body"))
	}

	if err := h.validator.Struct(&req); err != nil {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse("Validation failed: "+err.Error()))
	}

	userID, ok := auth.GetUserIDFromContext(c.Request().Context())
	if !ok || userID == "" {
		return c.JSON(http.StatusUnauthorized, utils.NewErrorResponse("invalid token: missing user_id"))
	}

	// Convert cart items
	var cartItems []*proto_voucher_v1.CartItem
	for _, item := range req.CartItems {
		cartItem := &proto_voucher_v1.CartItem{
			Quantity: item.Quantity,
			Price:    item.Price,
		}
		if item.ProductID != nil {
			cartItem.ProductId = item.ProductID
		}
		if item.CategoryID != nil {
			cartItem.CategoryId = item.CategoryID
		}
		cartItems = append(cartItems, cartItem)
	}

	orderTimestamp := time.Now()
	if req.OrderTimestamp != nil {
		orderTimestamp = *req.OrderTimestamp
	}

	protoReq := &proto_voucher_v1.CheckVoucherEligibilityRequest{
		Metadata: &proto_common_v1.RequestMetadata{
			RequestId: c.Response().Header().Get(echo.HeaderXRequestID),
			UserId:    userID,
		},
		VoucherCode:    req.VoucherCode,
		UserId:         userID,
		OrderAmount:    req.OrderAmount,
		OrderTimestamp: timestamppb.New(orderTimestamp),
		CartItems:      cartItems,
	}

	resp, err := h.voucherClient.CheckVoucherEligibility(c.Request().Context(), protoReq)
	if err != nil {
		return utils.HandleGRPCError(c, err, h.logger)
	}

	response := &dto.VoucherEligibilityResponse{
		Eligible:       resp.Eligible,
		Message:        resp.Message,
		DiscountAmount: resp.DiscountAmount,
	}

	if resp.VoucherId != "" {
		response.VoucherID = &resp.VoucherId
	}

	return c.JSON(http.StatusOK, response)
}

// ListAutoEligibleVouchers retrieves eligible auto-apply vouchers
func (h *VoucherHandler) HandleListAutoEligibleVouchers(c echo.Context) error {
	var req dto.ListAutoEligibleVouchersRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse("Invalid request body"))
	}

	if err := h.validator.Struct(&req); err != nil {
		return c.JSON(http.StatusBadRequest, utils.NewErrorResponse("Validation failed: "+err.Error()))
	}

	userID, ok := auth.GetUserIDFromContext(c.Request().Context())
	if !ok || userID == "" {
		return c.JSON(http.StatusUnauthorized, utils.NewErrorResponse("invalid token: missing user_id"))
	}

	// Convert cart items
	var cartItems []*proto_voucher_v1.CartItem
	for _, item := range req.CartItems {
		cartItem := &proto_voucher_v1.CartItem{
			Quantity: item.Quantity,
			Price:    item.Price,
		}
		if item.ProductID != nil {
			cartItem.ProductId = item.ProductID
		}
		if item.CategoryID != nil {
			cartItem.CategoryId = item.CategoryID
		}
		cartItems = append(cartItems, cartItem)
	}

	orderTimestamp := time.Now()
	if req.OrderTimestamp != nil {
		orderTimestamp = *req.OrderTimestamp
	}

	protoReq := &proto_voucher_v1.ListAutoEligibleVouchersRequest{
		Metadata: &proto_common_v1.RequestMetadata{
			RequestId: c.Response().Header().Get(echo.HeaderXRequestID),
			UserId:    userID,
		},
		UserId:         userID,
		OrderAmount:    req.OrderAmount,
		OrderTimestamp: timestamppb.New(orderTimestamp),
		CartItems:      cartItems,
	}

	resp, err := h.voucherClient.ListAutoEligibleVouchers(c.Request().Context(), protoReq)
	if err != nil {
		return utils.HandleGRPCError(c, err, h.logger)
	}

	// Convert to response DTOs
	var eligibleVouchers []*dto.EligibleVoucherResponse
	for _, voucher := range resp.Vouchers {
		eligibleVoucher := &dto.EligibleVoucherResponse{
			Eligible:       voucher.Eligible,
			Voucher:        dto.ToVoucherResponse(voucher.Voucher),
			DiscountAmount: voucher.DiscountAmount,
		}
		eligibleVouchers = append(eligibleVouchers, eligibleVoucher)
	}

	return c.JSON(http.StatusOK, eligibleVouchers)
}
