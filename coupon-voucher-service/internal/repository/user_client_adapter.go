package repository

import (
	"context"

	proto_user_v1 "gitlab.zalopay.vn/phunn4/coupon-proto/gen/go/user/v1"
	"gitlab.zalopay.vn/phunn4/coupon-voucher-service/internal/clients"
)

// UserClientAdapter adapts the clients.UserClient to implement UserClientInterface
type UserClientAdapter struct {
	client *clients.UserClient
}

func NewUserClientAdapter(client *clients.UserClient) UserClientInterface {
	if client == nil {
		return nil
	}
	return &UserClientAdapter{client: client}
}

func (a *UserClientAdapter) GetUser(ctx context.Context, userID string) (*UserInfo, error) {
	if a.client == nil {
		return nil, nil
	}

	user, err := a.client.GetUser(ctx, userID)
	if err != nil {
		return nil, err
	}

	return &UserInfo{
		ID:        user.Id,
		Type:      convertUserTypeToString(user.Type),
		CreatedAt: user.CreatedAt.AsTime(),
	}, nil
}

// convertUserTypeToString converts proto UserType enum to string
func convertUserTypeToString(userType proto_user_v1.UserType) string {
	switch userType {
	case proto_user_v1.UserType_USER_TYPE_NEW:
		return "NEW"
	case proto_user_v1.UserType_USER_TYPE_VIP:
		return "VIP"
	default:
		return "NEW"
	}
}
