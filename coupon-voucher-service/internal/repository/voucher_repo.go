package repository

import (
	"context"
	"fmt"
	"time"

	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/database"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/logging"
	"gitlab.zalopay.vn/phunn4/coupon-shared-libs/redis"
	"gitlab.zalopay.vn/phunn4/coupon-voucher-service/internal/model"
	"gorm.io/gorm"
)

type VoucherRepository interface {
	// CRUD operations
	Create(ctx context.Context, req *model.CreateVoucherRequest, createdBy string) (*model.Voucher, error)
	GetByID(ctx context.Context, id string) (*model.Voucher, error)
	GetByCode(ctx context.Context, code string) (*model.Voucher, error)
	Update(ctx context.Context, id string, req *model.UpdateVoucherRequest) error
	List(ctx context.Context, req *model.ListVouchersRequest) ([]*model.Voucher, int, error)

	// Discount types
	GetDiscountTypes(ctx context.Context) ([]*model.DiscountType, error)

	// Eligibility and auto-apply
	GetActiveAutoVouchers(ctx context.Context, orderAmount float64) ([]*model.Voucher, error)
	CheckVoucherEligibility(ctx context.Context, req *model.VoucherEligibilityRequest) (*model.VoucherEligibilityResponse, error)
	GetEligibleAutoVouchers(ctx context.Context, req *model.AutoVoucherEligibilityRequest) ([]*model.EligibleVoucher, error)

	// Usage tracking
	IncrementUsageCount(ctx context.Context, id string) error

	// Related data operations
	GetUserEligibilityRules(ctx context.Context, voucherID string) ([]*model.VoucherUserEligibility, error)
	ReplaceProductRestrictions(ctx context.Context, voucherID string, restrictions []*model.VoucherProductRestriction) error
	ReplaceTimeRestrictions(ctx context.Context, voucherID string, restrictions []*model.VoucherTimeRestriction) error
	ReplaceUserEligibility(ctx context.Context, voucherID string, rules []*model.VoucherUserEligibility) error
}

type voucherRepository struct {
	db          *database.DB
	redis       *redis.Client
	logger      *logging.Logger
	orderClient OrderClientInterface
	userClient  UserClientInterface
}

// OrderClientInterface defines the interface for order service operations
type OrderClientInterface interface {
	GetUserOrderCount(ctx context.Context, userID string) (int64, error)
	GetUserVoucherUsageCount(ctx context.Context, userID, voucherID string) (int32, error)
}

// UserClientInterface defines the interface for user service operations
type UserClientInterface interface {
	GetUser(ctx context.Context, userID string) (*UserInfo, error)
}

// UserInfo represents user information needed for eligibility checking
type UserInfo struct {
	ID        string
	Type      string
	CreatedAt time.Time
}

func NewVoucherRepository(db *database.DB, redis *redis.Client, logger *logging.Logger, orderClient OrderClientInterface, userClient UserClientInterface) VoucherRepository {
	return &voucherRepository{db: db, redis: redis, logger: logger, orderClient: orderClient, userClient: userClient}
}

// Create creates a new voucher
func (r *voucherRepository) Create(ctx context.Context, req *model.CreateVoucherRequest, createdBy string) (*model.Voucher, error) {
	voucher := &model.Voucher{
		VoucherCode:       req.VoucherCode,
		Title:             req.Title,
		Description:       req.Description,
		DiscountTypeID:    req.DiscountTypeID,
		DiscountValue:     req.DiscountValue,
		UsageMethod:       req.UsageMethod,
		ValidFrom:         req.ValidFrom,
		ValidUntil:        req.ValidUntil,
		MaxUsageCount:     req.MaxUsageCount,
		MaxUsagePerUser:   req.MaxUsagePerUser,
		MinOrderAmount:    req.MinOrderAmount,
		MaxDiscountAmount: req.MaxDiscountAmount,
		CreatedBy:         createdBy,
		Status:            model.VoucherStatusActive,
		UserEligibility:   "ALL",
	}

	if err := r.db.WithContext(ctx).Create(voucher).Error; err != nil {
		return nil, err
	}

	return r.GetByID(ctx, voucher.ID)
}

// GetByID retrieves a voucher by ID with all related data
func (r *voucherRepository) GetByID(ctx context.Context, id string) (*model.Voucher, error) {
	var voucher model.Voucher
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&voucher).Error; err != nil {
		return nil, err
	}

	// Load related data
	if err := r.loadRelatedData(ctx, &voucher); err != nil {
		r.logger.WithContext(ctx).Warnf("Failed to load related data for voucher %s: %v", id, err)
	}

	return &voucher, nil
}

// GetByCode retrieves a voucher by code
func (r *voucherRepository) GetByCode(ctx context.Context, code string) (*model.Voucher, error) {
	var voucher model.Voucher
	if err := r.db.WithContext(ctx).Where("voucher_code = ?", code).First(&voucher).Error; err != nil {
		return nil, err
	}

	// Load related data
	if err := r.loadRelatedData(ctx, &voucher); err != nil {
		r.logger.WithContext(ctx).Warnf("Failed to load related data for voucher %s: %v", code, err)
	}

	return &voucher, nil
}

func (r *voucherRepository) GetActiveAutoVouchers(ctx context.Context, orderAmount float64) ([]*model.Voucher, error) {
	var vouchers []*model.Voucher
	err := r.db.WithContext(ctx).
		Where("usage_method = ?", model.UsageMethodAutomatic).
		Where("status = ?", model.VoucherStatusActive).
		Where("valid_from <= ?", time.Now()).
		Where("valid_until >= ?", time.Now()).
		Where("min_order_amount <= ?", orderAmount).
		Where("max_usage_count IS NULL OR current_usage_count < max_usage_count").
		Find(&vouchers).Error
	return vouchers, err
}

// Helper method to load related data for a voucher
func (r *voucherRepository) loadRelatedData(ctx context.Context, voucher *model.Voucher) error {
	// Load discount type
	var discountType model.DiscountType
	if err := r.db.WithContext(ctx).Where("id = ?", voucher.DiscountTypeID).First(&discountType).Error; err == nil {
		voucher.DiscountType = &discountType
	}

	// Load product restrictions
	var productRestrictions []*model.VoucherProductRestriction
	if err := r.db.WithContext(ctx).Where("voucher_id = ?", voucher.ID).Find(&productRestrictions).Error; err == nil {
		voucher.ProductRestrictions = productRestrictions
	}

	// Load time restrictions
	var timeRestrictions []*model.VoucherTimeRestriction
	if err := r.db.WithContext(ctx).Where("voucher_id = ?", voucher.ID).Find(&timeRestrictions).Error; err == nil {
		voucher.TimeRestrictions = timeRestrictions
	}

	// Load user eligibility rules
	var userEligibility []*model.VoucherUserEligibility
	if err := r.db.WithContext(ctx).Where("voucher_id = ?", voucher.ID).Find(&userEligibility).Error; err == nil {
		voucher.UserEligibilityRules = userEligibility
	}

	return nil
}

// Update updates a voucher
func (r *voucherRepository) Update(ctx context.Context, id string, req *model.UpdateVoucherRequest) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Update main voucher record
		updates := map[string]interface{}{
			"title":               req.Title,
			"description":         req.Description,
			"discount_type_id":    req.DiscountTypeID,
			"discount_value":      req.DiscountValue,
			"usage_method":        req.UsageMethod,
			"status":              req.Status,
			"min_order_amount":    req.MinOrderAmount,
			"max_discount_amount": req.MaxDiscountAmount,
			"max_usage_count":     req.MaxUsageCount,
			"max_usage_per_user":  req.MaxUsagePerUser,
			"valid_from":          req.ValidFrom,
			"valid_until":         req.ValidUntil,
		}

		if err := tx.Model(&model.Voucher{}).Where("id = ?", id).Updates(updates).Error; err != nil {
			return err
		}

		// Replace product restrictions
		if err := r.replaceProductRestrictionsInTx(ctx, tx, id, req.ProductRestrictions); err != nil {
			return err
		}

		// Replace time restrictions
		if err := r.replaceTimeRestrictionsInTx(ctx, tx, id, req.TimeRestrictions); err != nil {
			return err
		}

		// Replace user eligibility rules
		if err := r.replaceUserEligibilityInTx(ctx, tx, id, req.UserEligibility); err != nil {
			return err
		}

		return nil
	})
}

// List retrieves vouchers with pagination and filtering
func (r *voucherRepository) List(ctx context.Context, req *model.ListVouchersRequest) ([]*model.Voucher, int, error) {
	var vouchers []*model.Voucher
	var total int64

	query := r.db.WithContext(ctx).Model(&model.Voucher{})

	// Apply filters
	if req.Search != "" {
		query = query.Where("title ILIKE ? OR voucher_code ILIKE ? OR description ILIKE ?",
			"%"+req.Search+"%", "%"+req.Search+"%", "%"+req.Search+"%")
	}

	if req.DiscountTypeID != nil {
		query = query.Where("discount_type_id = ?", *req.DiscountTypeID)
	}

	if req.UsageMethod != nil {
		query = query.Where("usage_method = ?", *req.UsageMethod)
	}

	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	sortBy := req.SortBy
	if sortBy == "" {
		sortBy = "created_at"
	}
	sortOrder := req.SortOrder
	if sortOrder == "" {
		sortOrder = "desc"
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Apply pagination
	offset := (req.Page - 1) * req.Limit
	query = query.Offset(offset).Limit(req.Limit)

	if err := query.Find(&vouchers).Error; err != nil {
		return nil, 0, err
	}

	// Load related data for each voucher
	for _, voucher := range vouchers {
		if err := r.loadRelatedData(ctx, voucher); err != nil {
			r.logger.WithContext(ctx).Warnf("Failed to load related data for voucher %s: %v", voucher.ID, err)
		}
	}

	return vouchers, int(total), nil
}

// GetDiscountTypes retrieves all discount types
func (r *voucherRepository) GetDiscountTypes(ctx context.Context) ([]*model.DiscountType, error) {
	var discountTypes []*model.DiscountType
	err := r.db.WithContext(ctx).Where("is_active = ?", true).Find(&discountTypes).Error
	return discountTypes, err
}

// CheckVoucherEligibility checks if a voucher is eligible for use with complex logic
func (r *voucherRepository) CheckVoucherEligibility(ctx context.Context, req *model.VoucherEligibilityRequest) (*model.VoucherEligibilityResponse, error) {
	// Get voucher details with discount type
	voucher, err := r.GetByCode(ctx, req.VoucherCode)
	if err != nil {
		return &model.VoucherEligibilityResponse{
			Eligible: false,
			Message:  "Voucher not found or inactive",
		}, nil
	}

	if voucher.Status != model.VoucherStatusActive {
		return &model.VoucherEligibilityResponse{
			Eligible: false,
			Message:  "Voucher not found or inactive",
		}, nil
	}

	// Validity period check
	orderTimestamp := req.OrderTimestamp
	if orderTimestamp.IsZero() {
		orderTimestamp = time.Now()
	}

	if orderTimestamp.Before(voucher.ValidFrom) || orderTimestamp.After(voucher.ValidUntil) {
		return &model.VoucherEligibilityResponse{
			Eligible: false,
			Message:  "Voucher is expired or not yet valid",
		}, nil
	}

	// Global usage limit
	if voucher.MaxUsageCount != nil && voucher.CurrentUsageCount >= *voucher.MaxUsageCount {
		return &model.VoucherEligibilityResponse{
			Eligible: false,
			Message:  "Voucher usage limit exceeded globally",
		}, nil
	}

	// Per-user usage limit - count from orders table (simulated for now)
	userUsageCount, err := r.getUserVoucherUsageCount(ctx, req.UserID, voucher.ID)
	if err != nil {
		r.logger.WithContext(ctx).Warnf("Failed to get user voucher usage count: %v", err)
		userUsageCount = 0 // Continue with 0 if we can't get the count
	}

	if voucher.MaxUsagePerUser != nil && userUsageCount >= *voucher.MaxUsagePerUser {
		return &model.VoucherEligibilityResponse{
			Eligible: false,
			Message:  "User has exceeded usage limit for this voucher",
		}, nil
	}

	// Check user eligibility rules
	eligible, message, err := r.checkUserEligibility(ctx, voucher, req.UserID, orderTimestamp)
	if err != nil {
		return nil, err
	}
	if !eligible {
		return &model.VoucherEligibilityResponse{
			Eligible: false,
			Message:  message,
		}, nil
	}

	// Check time restrictions
	eligible, message, err = r.checkTimeRestrictions(ctx, voucher, orderTimestamp)
	if err != nil {
		return nil, err
	}
	if !eligible {
		return &model.VoucherEligibilityResponse{
			Eligible: false,
			Message:  message,
		}, nil
	}

	// Check product/category restrictions and calculate eligible order amount
	eligibleOrderAmount, err := r.checkProductRestrictions(ctx, voucher, req.CartItems, req.OrderAmount)
	if err != nil {
		return nil, err
	}
	if eligibleOrderAmount == 0 {
		return &model.VoucherEligibilityResponse{
			Eligible: false,
			Message:  "No eligible products in cart for this voucher",
		}, nil
	}

	// Use eligible order amount for minimum order check and discount calculation
	orderAmountForCalculation := eligibleOrderAmount
	if len(req.CartItems) == 0 {
		// If no cart items provided, use original order amount
		orderAmountForCalculation = req.OrderAmount
	}

	// Minimum order amount check
	if orderAmountForCalculation < voucher.MinOrderAmount {
		return &model.VoucherEligibilityResponse{
			Eligible: false,
			Message:  "Minimum order amount not met",
		}, nil
	}

	// Calculate discount
	discountAmount := r.calculateDiscount(voucher, orderAmountForCalculation)

	return &model.VoucherEligibilityResponse{
		Eligible:       true,
		Message:        "Voucher is eligible",
		VoucherID:      &voucher.ID,
		DiscountAmount: discountAmount,
	}, nil
}

// GetEligibleAutoVouchers retrieves eligible auto-apply vouchers
func (r *voucherRepository) GetEligibleAutoVouchers(ctx context.Context, req *model.AutoVoucherEligibilityRequest) ([]*model.EligibleVoucher, error) {
	vouchers, err := r.GetActiveAutoVouchers(ctx, req.OrderAmount)
	if err != nil {
		return nil, err
	}

	var eligibleVouchers []*model.EligibleVoucher
	for _, voucher := range vouchers {
		discountAmount := r.calculateDiscount(voucher, req.OrderAmount)
		eligibleVouchers = append(eligibleVouchers, &model.EligibleVoucher{
			Eligible:       true,
			Voucher:        voucher,
			DiscountAmount: discountAmount,
		})
	}

	return eligibleVouchers, nil
}

// getUserVoucherUsageCount gets the number of times a user has used a specific voucher
func (r *voucherRepository) getUserVoucherUsageCount(ctx context.Context, userID, voucherID string) (int, error) {
	if r.orderClient == nil {
		r.logger.WithContext(ctx).Warn("Order client not available, returning 0 for voucher usage count")
		return 0, nil
	}

	count, err := r.orderClient.GetUserVoucherUsageCount(ctx, userID, voucherID)
	if err != nil {
		r.logger.WithContext(ctx).Warnf("Failed to get user voucher usage count from order service: %v", err)
		return 0, err
	}

	return int(count), nil
}

// getUserOrderCount gets the total number of orders a user has made
func (r *voucherRepository) getUserOrderCount(ctx context.Context, userID string) (int64, error) {
	if r.orderClient == nil {
		r.logger.WithContext(ctx).Warn("Order client not available, returning 0 for order count")
		return 0, nil
	}

	count, err := r.orderClient.GetUserOrderCount(ctx, userID)
	if err != nil {
		r.logger.WithContext(ctx).Warnf("Failed to get user order count from order service: %v", err)
		return 0, err
	}

	return count, nil
}

// checkUserEligibility checks if a user is eligible for a voucher based on user eligibility rules
func (r *voucherRepository) checkUserEligibility(ctx context.Context, voucher *model.Voucher, userID string, orderTimestamp time.Time) (bool, string, error) {
	// Get user eligibility rules for this voucher
	rules, err := r.GetUserEligibilityRules(ctx, voucher.ID)
	if err != nil {
		return false, "", err
	}

	// If no rules exist, voucher is available to all users
	if len(rules) == 0 {
		return true, "", nil
	}

	// Get user information from user service
	var userAccountAgeDays int32 = 30 // Default fallback
	var userOrderCount int64 = 0      // Default fallback
	var userType string = "NEW"       // Default fallback

	if r.userClient != nil {
		userInfo, err := r.userClient.GetUser(ctx, userID)
		if err != nil {
			r.logger.WithContext(ctx).Warnf("Failed to get user info from user service: %v", err)
		} else if userInfo != nil {
			userType = userInfo.Type
			// Calculate account age in days
			accountAge := orderTimestamp.Sub(userInfo.CreatedAt)
			userAccountAgeDays = int32(accountAge.Hours() / 24)
		}
	}

	// Get actual user order count
	actualOrderCount, err := r.getUserOrderCount(ctx, userID)
	if err != nil {
		r.logger.WithContext(ctx).Warnf("Failed to get user order count: %v", err)
		// Continue with default data
	} else {
		userOrderCount = actualOrderCount
	}

	// Check each rule
	for _, rule := range rules {
		// Specific user ID check
		if rule.UserID != nil && *rule.UserID == userID {
			return true, "", nil
		}

		// User type check
		if rule.UserType != nil && *rule.UserType == userType {
			// Additional checks for this user type
			if rule.MinAccountAgeDays != nil && userAccountAgeDays < *rule.MinAccountAgeDays {
				return false, "User account age requirement not met", nil
			}
			if rule.MaxAccountAgeDays != nil && userAccountAgeDays > *rule.MaxAccountAgeDays {
				return false, "User account age exceeds maximum allowed", nil
			}
			if rule.MinPreviousOrders != nil && userOrderCount < *rule.MinPreviousOrders {
				return false, "User does not meet minimum order requirement", nil
			}
			if rule.MaxPreviousOrders != nil && userOrderCount > *rule.MaxPreviousOrders {
				return false, "User exceeds maximum order limit", nil
			}
			return true, "", nil
		}

		// VIP user restriction
		if rule.UserType != nil && *rule.UserType == "VIP" && userType != "VIP" {
			return false, "Voucher is only for VIP users", nil
		}

		// New user checks (based on order count)
		if rule.MinPreviousOrders != nil && *rule.MinPreviousOrders == 0 && rule.UserID == nil {
			if userOrderCount > 0 {
				return false, "Voucher is only for new users", nil
			}
			return true, "", nil
		}

		// Existing user checks
		if rule.MinPreviousOrders != nil && *rule.MinPreviousOrders > 0 && rule.UserID == nil {
			if userOrderCount == 0 {
				return false, "Voucher is only for existing users", nil
			}
			return true, "", nil
		}

		// Account age based checks
		if rule.MinAccountAgeDays != nil || rule.MaxAccountAgeDays != nil {
			if rule.MaxAccountAgeDays != nil && *rule.MaxAccountAgeDays <= 30 {
				if userAccountAgeDays > 30 {
					return false, "Voucher is only for new users", nil
				}
			} else {
				if userAccountAgeDays <= 30 {
					return false, "Voucher is only for existing users", nil
				}
			}
			return true, "", nil
		}
	}

	// If we have rules but none matched, user is not eligible
	return false, "Voucher is not available for this user", nil
}

// IncrementUsageCount increments the usage count for a voucher
func (r *voucherRepository) IncrementUsageCount(ctx context.Context, id string) error {
	return r.db.WithContext(ctx).Model(&model.Voucher{}).
		Where("id = ?", id).
		UpdateColumn("current_usage_count", gorm.Expr("current_usage_count + 1")).Error
}

// checkTimeRestrictions checks if the current time meets voucher time restrictions
func (r *voucherRepository) checkTimeRestrictions(ctx context.Context, voucher *model.Voucher, orderTimestamp time.Time) (bool, string, error) {
	// Get time restrictions for this voucher
	var timeRestrictions []*model.VoucherTimeRestriction
	err := r.db.WithContext(ctx).Where("voucher_id = ?", voucher.ID).Find(&timeRestrictions).Error
	if err != nil {
		return false, "", err
	}

	// If no time restrictions exist, voucher is valid at any time
	if len(timeRestrictions) == 0 {
		return true, "", nil
	}

	localTimestamp := orderTimestamp
	currentHour := int32(localTimestamp.Hour())
	currentDayOfWeek := int32(localTimestamp.Weekday())
	currentDate := localTimestamp.Truncate(24 * time.Hour)

	// Check each time restriction
	for _, restriction := range timeRestrictions {
		valid := false

		switch restriction.RestrictionType {
		case model.TimeRestrictionTypeDaysOfWeek:
			if len(restriction.AllowedDaysOfWeek) > 0 {
				for _, allowedDay := range restriction.AllowedDaysOfWeek {
					if currentDayOfWeek == allowedDay {
						valid = true
						break
					}
				}
			}

		case model.TimeRestrictionTypeHoursOfDay:
			if restriction.AllowedHoursStart != nil && restriction.AllowedHoursEnd != nil {
				start := *restriction.AllowedHoursStart
				end := *restriction.AllowedHoursEnd

				if start <= end {
					// Normal range (e.g., 9 to 17)
					valid = currentHour >= start && currentHour <= end
				} else {
					// Overnight range (e.g., 22 to 6)
					valid = currentHour >= start || currentHour <= end
				}
			}

		case model.TimeRestrictionTypeSpecificDates:
			if len(restriction.SpecificDates) > 0 {
				for _, specificDate := range restriction.SpecificDates {
					if currentDate.Equal(specificDate.Truncate(24 * time.Hour)) {
						valid = true
						break
					}
				}
			}

		case model.TimeRestrictionTypeRecurringDates:
			if restriction.RecurrencePattern != nil {
				valid = r.checkRecurrencePattern(localTimestamp, *restriction.RecurrencePattern, restriction)
			}

		default:
			valid = true
		}

		if !valid {
			return false, "Voucher is not valid at this time", nil
		}
	}

	return true, "", nil
}

// checkRecurrencePattern checks if the current time matches a recurrence pattern
func (r *voucherRepository) checkRecurrencePattern(timestamp time.Time, pattern model.RecurrencePattern, restriction *model.VoucherTimeRestriction) bool {
	switch pattern {
	case model.RecurrencePatternDaily:
		return true

	case model.RecurrencePatternWeekly:
		if restriction.RecurrenceDayOfWeek != nil {
			return int32(timestamp.Weekday()) == *restriction.RecurrenceDayOfWeek
		}

	case model.RecurrencePatternMonthly:
		if restriction.RecurrenceDayOfMonth != nil {
			return int32(timestamp.Day()) == *restriction.RecurrenceDayOfMonth
		}

	case model.RecurrencePatternYearly:
		if restriction.RecurrenceMonth != nil && restriction.RecurrenceDayOfMonth != nil {
			return int32(timestamp.Month()) == *restriction.RecurrenceMonth &&
				int32(timestamp.Day()) == *restriction.RecurrenceDayOfMonth
		}

	case model.RecurrencePatternQuarterly:
		if restriction.RecurrenceDayOfMonth != nil {
			return int32(timestamp.Day()) == *restriction.RecurrenceDayOfMonth &&
				(int32(timestamp.Month())%3) == (*restriction.RecurrenceMonth%3)
		}
	}

	return false
}

// checkProductRestrictions checks product/category restrictions and returns eligible order amount
func (r *voucherRepository) checkProductRestrictions(ctx context.Context, voucher *model.Voucher, cartItems []model.CartItem, orderAmount float64) (float64, error) {
	// Get product restrictions for this voucher
	var productRestrictions []*model.VoucherProductRestriction
	err := r.db.WithContext(ctx).Where("voucher_id = ?", voucher.ID).Find(&productRestrictions).Error
	if err != nil {
		return 0, err
	}

	// If no product restrictions exist, all products are eligible
	if len(productRestrictions) == 0 {
		return orderAmount, nil
	}

	// If no cart items provided, we can't check product restrictions
	if len(cartItems) == 0 {
		return orderAmount, nil
	}

	eligibleOrderAmount := 0.0

	// Check each cart item against restrictions
	for _, cartItem := range cartItems {
		productAllowed := false

		// Check if product is explicitly included
		for _, restriction := range productRestrictions {
			if restriction.IsIncluded {
				if (restriction.ProductID != nil && cartItem.ProductID != nil && *restriction.ProductID == *cartItem.ProductID) ||
					(restriction.CategoryID != nil && cartItem.CategoryID != nil && *restriction.CategoryID == *cartItem.CategoryID) {
					productAllowed = true
					break
				}
			}
		}

		// If not explicitly included, check if it's excluded
		if !productAllowed {
			productAllowed = true // Assume allowed unless explicitly excluded
			for _, restriction := range productRestrictions {
				if !restriction.IsIncluded {
					if (restriction.ProductID != nil && cartItem.ProductID != nil && *restriction.ProductID == *cartItem.ProductID) ||
						(restriction.CategoryID != nil && cartItem.CategoryID != nil && *restriction.CategoryID == *cartItem.CategoryID) {
						productAllowed = false
						break
					}
				}
			}
		}

		// If no inclusion rules exist, check if there are any inclusion restrictions at all
		if !productAllowed {
			hasInclusionRules := false
			for _, restriction := range productRestrictions {
				if restriction.IsIncluded {
					hasInclusionRules = true
					break
				}
			}
			// If no inclusion rules exist, product is allowed unless explicitly excluded
			if !hasInclusionRules {
				productAllowed = true
			}
		}

		if productAllowed {
			eligibleOrderAmount += cartItem.Price
		}
	}

	return eligibleOrderAmount, nil
}

// GetUserEligibilityRules retrieves user eligibility rules for a voucher
func (r *voucherRepository) GetUserEligibilityRules(ctx context.Context, voucherID string) ([]*model.VoucherUserEligibility, error) {
	var rules []*model.VoucherUserEligibility
	err := r.db.WithContext(ctx).Where("voucher_id = ?", voucherID).Find(&rules).Error
	return rules, err
}

// ReplaceProductRestrictions replaces product restrictions for a voucher
func (r *voucherRepository) ReplaceProductRestrictions(ctx context.Context, voucherID string, restrictions []*model.VoucherProductRestriction) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return r.replaceProductRestrictionsInTx(ctx, tx, voucherID, restrictions)
	})
}

// ReplaceTimeRestrictions replaces time restrictions for a voucher
func (r *voucherRepository) ReplaceTimeRestrictions(ctx context.Context, voucherID string, restrictions []*model.VoucherTimeRestriction) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return r.replaceTimeRestrictionsInTx(ctx, tx, voucherID, restrictions)
	})
}

// ReplaceUserEligibility replaces user eligibility rules for a voucher
func (r *voucherRepository) ReplaceUserEligibility(ctx context.Context, voucherID string, rules []*model.VoucherUserEligibility) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		return r.replaceUserEligibilityInTx(ctx, tx, voucherID, rules)
	})
}

// Helper methods for transaction-based operations
func (r *voucherRepository) replaceProductRestrictionsInTx(ctx context.Context, tx *gorm.DB, voucherID string, restrictions []*model.VoucherProductRestriction) error {
	// Delete existing restrictions
	if err := tx.Where("voucher_id = ?", voucherID).Delete(&model.VoucherProductRestriction{}).Error; err != nil {
		return err
	}

	// Insert new restrictions
	for _, restriction := range restrictions {
		restriction.VoucherID = voucherID
		if err := tx.Create(restriction).Error; err != nil {
			return err
		}
	}

	return nil
}

func (r *voucherRepository) replaceTimeRestrictionsInTx(ctx context.Context, tx *gorm.DB, voucherID string, restrictions []*model.VoucherTimeRestriction) error {
	// Delete existing restrictions
	if err := tx.Where("voucher_id = ?", voucherID).Delete(&model.VoucherTimeRestriction{}).Error; err != nil {
		return err
	}

	// Insert new restrictions
	for _, restriction := range restrictions {
		restriction.VoucherID = voucherID
		if err := tx.Create(restriction).Error; err != nil {
			return err
		}
	}

	return nil
}

func (r *voucherRepository) replaceUserEligibilityInTx(ctx context.Context, tx *gorm.DB, voucherID string, rules []*model.VoucherUserEligibility) error {
	// Delete existing rules
	if err := tx.Where("voucher_id = ?", voucherID).Delete(&model.VoucherUserEligibility{}).Error; err != nil {
		return err
	}

	// Insert new rules
	for _, rule := range rules {
		rule.VoucherID = voucherID
		if err := tx.Create(rule).Error; err != nil {
			return err
		}
	}

	return nil
}

// calculateDiscount calculates the discount amount for a voucher
func (r *voucherRepository) calculateDiscount(voucher *model.Voucher, orderAmount float64) float64 {
	var discount float64

	// Get discount type to determine calculation method
	if voucher.DiscountType != nil {
		switch voucher.DiscountType.TypeCode {
		case "PERCENT":
			// PERCENT: discount = order_amount * (discount_value / 100)
			discount = orderAmount * (voucher.DiscountValue / 100)
		case "FIXED":
			// FIXED: subtract a fixed amount from order
			discount = voucher.DiscountValue
		case "FLAT":
			// FLAT: set final total to discount_value (discount = order_amount - discount_value)
			discount = orderAmount - voucher.DiscountValue
			if discount < 0 {
				discount = orderAmount // Cannot discount more than order amount
			}
		default:
			// Unknown type: no discount
			discount = 0
		}
	} else {
		// Default to percentage if discount type is not loaded
		discount = orderAmount * (voucher.DiscountValue / 100)
	}

	// Apply max discount cap if exists
	if voucher.MaxDiscountAmount != nil && discount > *voucher.MaxDiscountAmount {
		discount = *voucher.MaxDiscountAmount
	}

	// Cannot exceed order amount
	if discount > orderAmount {
		discount = orderAmount
	}

	// Round to two decimal places
	discount = float64(int(discount*100+0.5)) / 100

	return discount
}
